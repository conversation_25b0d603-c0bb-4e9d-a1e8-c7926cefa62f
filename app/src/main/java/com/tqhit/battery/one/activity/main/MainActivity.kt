package com.tqhit.battery.one.activity.main

import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
//import com.applovin.mediation.MaxAd
//import com.applovin.mediation.MaxAdViewAdListener
//import com.applovin.mediation.MaxError
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ActivityMainBinding
import com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository
// Legacy NewChargeFragment import removed - using StatsChargeFragment instead
// Legacy ChargeMonitorServiceHelper import removed - replaced by UnifiedBatteryNotificationService
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper
import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.service.BatteryMonitorService
import com.tqhit.battery.one.service.ChargingOverlayService
import com.tqhit.battery.one.utils.AntiThiefUtils
import com.tqhit.battery.one.ads.core.ApplovinBannerAdManager
import com.tqhit.battery.one.utils.DeviceUtils
import com.tqhit.battery.one.utils.BackgroundPermissionManager
import com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : AdLibBaseActivity<ActivityMainBinding>() {
    override val binding by lazy { ActivityMainBinding.inflate(layoutInflater) }

    // Use statsChargeRepository directly instead of BatteryViewModel to avoid crashes
    @Inject lateinit var statsChargeRepository: StatsChargeRepository
    @Inject lateinit var appRepository: AppRepository
    @Inject lateinit var applovinBannerAdManager: ApplovinBannerAdManager
    @Inject lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper
    // Legacy chargeMonitorServiceHelper injection removed - replaced by UnifiedBatteryNotificationService
    @Inject lateinit var enhancedDischargeTimerServiceHelper: EnhancedDischargeTimerServiceHelper
    @Inject lateinit var dischargeSessionRepository: DischargeSessionRepository
    @Inject lateinit var unifiedBatteryNotificationServiceHelper: UnifiedBatteryNotificationServiceHelper
    @Inject lateinit var usageStatsPermissionManager: UsageStatsPermissionManager
    @Inject lateinit var dynamicNavigationManager: DynamicNavigationManager
    @Inject lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider

    private var isInitialFragmentSet = false
    private var currentSelectedItemId: Int = R.id.chargeFragment
    private var isFragmentSetupInProgress = false
    private val handler = Handler(Looper.getMainLooper())
    private var savedInstanceState: Bundle? = null

    // Background permission dialog
    private var backgroundPermissionDialog: BackgroundPermissionDialog? = null

    // Add a receiver for handling animation-related events on Xiaomi devices
    private val animationFixReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "android.intent.action.SCREEN_ON" ||
                intent?.action == "android.intent.action.USER_PRESENT") {
                // When screen turns on or user unlocks, refresh the UI to prevent animation issues
                if (DeviceUtils.isXiaomiDevice()) {
                    try {
                        Log.d(TAG, "Screen state changed, refreshing UI for Xiaomi device")
                        binding.root.invalidate()
                        binding.navHostFragment.invalidate()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error handling screen state change", e)
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "NAVIGATION_RESTORE: onCreate: Starting main activity")

        // Store savedInstanceState for later use in navigation setup
        this.savedInstanceState = savedInstanceState

        // Handle state restoration with detailed logging
        val isStateRestoration = savedInstanceState != null
        Log.d(TAG, "NAVIGATION_RESTORE: Is state restoration: $isStateRestoration")

        savedInstanceState?.let {
            currentSelectedItemId = it.getInt("selected_item_id", R.id.chargeFragment)
            Log.d(TAG, "NAVIGATION_RESTORE: Restored selectedItemId: $currentSelectedItemId")
        }

        // Initialize usage stats permission manager early in lifecycle
        try {
            usageStatsPermissionManager.initializePermissionLauncher(this)
            Log.d(TAG, "UsageStatsPermissionManager initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing UsageStatsPermissionManager", e)
        }

        // Apply additional Xiaomi-specific handling if needed
        applyXiaomiSpecificHandling()

        // Check and show background permission dialog if needed
        checkAndShowBackgroundPermissionDialog()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        val selectedItemId = binding.bottomView.selectedItemId
        outState.putInt("selected_item_id", selectedItemId)
        Log.d(TAG, "NAVIGATION_RESTORE: Saving selectedItemId: $selectedItemId")
    }

    /**
     * Sets up dynamic navigation manager for real-time charging state switching
     */
    private fun setupDynamicNavigation() {
        Log.d(TAG, "NAVIGATION_RESTORE: Setting up dynamic navigation manager")

        try {
            // Check if we have a saved state to restore
            // Fix: Only consider it a restored state if we actually have saved instance state
            val hasRestoredState = savedInstanceState != null && currentSelectedItemId != R.id.chargeFragment
            Log.d(TAG, "NAVIGATION_RESTORE: Has restored state: $hasRestoredState, currentSelectedItemId: $currentSelectedItemId, savedInstanceState: ${savedInstanceState != null}")

            // Initialize the dynamic navigation manager with restoration context
            dynamicNavigationManager.initialize(
                fragmentManager = supportFragmentManager,
                bottomNavigationView = binding.bottomView,
                fragmentContainerId = binding.navHostFragment.id,
                lifecycleOwner = this,
                restoredSelectedItemId = if (hasRestoredState) currentSelectedItemId else null
            )

            // Set up bottom navigation listener to work with dynamic navigation
            binding.bottomView.setOnItemSelectedListener { item ->
                val navigationStartTime = System.currentTimeMillis()
                Log.d(TAG, "NAVIGATION_PERFORMANCE: Navigation item selected: ${item.itemId} at $navigationStartTime")

                // Let the dynamic navigation manager handle the navigation
                val handled = dynamicNavigationManager.handleUserNavigation(item.itemId)

                if (handled) {
                    currentSelectedItemId = item.itemId
                    val navigationTime = System.currentTimeMillis() - navigationStartTime
                    Log.d(TAG, "NAVIGATION_PERFORMANCE: Navigation handled by dynamic manager in ${navigationTime}ms")

                    // Log performance stats periodically
                    if (navigationTime > 100) { // Log if navigation takes more than 100ms
                        Log.i(TAG, "NAVIGATION_PERFORMANCE: Slow navigation detected (${navigationTime}ms)")
                        Log.i(TAG, dynamicNavigationManager.getPerformanceStats())
                    }
                } else {
                    Log.w(TAG, "NAVIGATION_RESTORE: Navigation not handled by dynamic manager, falling back to manual handling")
                    // Fallback to manual fragment switching for non-dynamic items
                    handleManualNavigation(item.itemId)
                    val fallbackTime = System.currentTimeMillis() - navigationStartTime
                    Log.d(TAG, "NAVIGATION_PERFORMANCE: Manual navigation completed in ${fallbackTime}ms")
                }

                handled
            }

            Log.d(TAG, "NAVIGATION_RESTORE: Dynamic navigation setup completed")
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_RESTORE: Error setting up dynamic navigation", e)
        }
    }

    /**
     * Handles manual navigation for items not managed by dynamic navigation
     */
    private fun handleManualNavigation(itemId: Int) {
        val fragment = when (itemId) {
            R.id.chargeFragment -> StatsChargeFragment()
            R.id.dischargeFragment -> DischargeFragment()
            R.id.healthFragment -> HealthFragment()
            R.id.settingsFragment -> SettingsFragment()
            R.id.animationGridFragment -> AnimationGridFragment()
            else -> return
        }

        Log.d(TAG, "Manual navigation to: ${fragment.javaClass.simpleName}")
        supportFragmentManager
            .beginTransaction()
            .replace(binding.navHostFragment.id, fragment)
            .commit()

        currentSelectedItemId = itemId
    }

    /**
     * Optimized fragment setup to prevent multiple recreations and improve startup performance
     * Now works with dynamic navigation manager
     */
    private fun setupInitialFragmentOptimized(startTime: Long) {
        // Prevent multiple concurrent fragment setups
        if (isFragmentSetupInProgress || isInitialFragmentSet) {
            Log.d(TAG, "Fragment setup already in progress or completed, skipping")
            return
        }

        isFragmentSetupInProgress = true

        lifecycleScope.launch {
            try {
                // Wait for dynamic navigation manager to be initialized with longer timeout
                var retryCount = 0
                while (!dynamicNavigationManager.isInitialized() && retryCount < 20) {
                    kotlinx.coroutines.delay(100)
                    retryCount++
                }

                if (!dynamicNavigationManager.isInitialized()) {
                    Log.w(TAG, "Dynamic navigation manager not initialized after 2 seconds, falling back to legacy setup")
                    setupLegacyInitialFragment(startTime)
                    return@launch
                }

                // Dynamic navigation manager will handle initial fragment setup
                // Just mark as initialized and sync the currentSelectedItemId
                withContext(Dispatchers.Main) {
                    isInitialFragmentSet = true
                    val currentState = dynamicNavigationManager.getCurrentState()

                    // CRITICAL FIX: Update currentSelectedItemId to match the dynamic navigation state
                    // This prevents restoreFragmentState() from overriding the animation fragment
                    currentState?.let { state ->
                        currentSelectedItemId = state.activeFragmentId
                        Log.d(TAG, "NAVIGATION_RESTORE: Updated currentSelectedItemId to match dynamic state: ${state.activeFragmentId}")
                    }

                    Log.d(TAG, "STARTUP_TIMING: Dynamic navigation setup completed in ${System.currentTimeMillis() - startTime}ms")
                    Log.d(TAG, "NAVIGATION_RESTORE: Dynamic navigation final state - activeFragment: ${currentState?.activeFragmentId}, isCharging: ${currentState?.isCharging}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in dynamic fragment setup", e)
                setupLegacyInitialFragment(startTime)
            } finally {
                isFragmentSetupInProgress = false
            }
        }
    }

    /**
     * Legacy fragment setup as fallback
     */
    private fun setupLegacyInitialFragment(startTime: Long) {
        lifecycleScope.launch {
            try {
                val batteryStatusStartTime = System.currentTimeMillis()

                // Use background thread for battery status retrieval with timeout to avoid blocking UI
                val initialBatteryStatus = withContext(Dispatchers.IO) {
                    try {
                        // Get status from CoreBatteryStatsProvider with timeout
                        withTimeout(500) {
                            coreBatteryStatsProvider.getCurrentStatus()
                                ?: com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.createDefault()
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "Timeout getting battery status from core provider", e)
                        // Return default status if timeout
                        com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.createDefault()
                    }
                }

                Log.d(TAG, "STARTUP_TIMING: Battery status retrieval took ${System.currentTimeMillis() - batteryStatusStartTime}ms")

                val isCharging = initialBatteryStatus.isCharging

                // Switch back to main thread for UI operations
                withContext(Dispatchers.Main) {
                    if (!isInitialFragmentSet) {
                        Log.d(TAG, "Legacy initial fragment setup - charging state: $isCharging")
                        val fragmentStartTime = System.currentTimeMillis()

                        val initialFragment = if (isCharging) StatsChargeFragment() else DischargeFragment()
                        val initialItemId = if (isCharging) R.id.chargeFragment else R.id.dischargeFragment

                        // Use commitNow() for immediate execution to prevent timing issues
                        supportFragmentManager
                            .beginTransaction()
                            .replace(binding.navHostFragment.id, initialFragment)
                            .commitNow()

                        binding.bottomView.selectedItemId = initialItemId
                        currentSelectedItemId = initialItemId
                        isInitialFragmentSet = true

                        Log.d(TAG, "STARTUP_TIMING: Legacy fragment setup took ${System.currentTimeMillis() - fragmentStartTime}ms")
                    }

                    Log.d(TAG, "STARTUP_TIMING: MainActivity.setupUI() completed in ${System.currentTimeMillis() - startTime}ms")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in legacy fragment setup", e)
            }
        }
    }

    private fun isServiceRunning(serviceClass: Class<*>): Boolean {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return activityManager.getRunningServices(Integer.MAX_VALUE)
                .any { it.service.className == serviceClass.name }
    }

    override fun setupUI() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: MainActivity.setupUI() started at $startTime")

        super.setupUI()
        Log.d(TAG, "setupUI: Setting up main UI")

        // Initialize dynamic navigation manager
        setupDynamicNavigation()

        // Set initial fragment based on charging state only once with optimization
        setupInitialFragmentOptimized(startTime)

        // --- Banner Ad Integration ---
        initBannerAd()
    }

    private fun restoreFragmentState() {
        Log.d(TAG, "NAVIGATION_RESTORE: restoreFragmentState called - isInitialFragmentSet: $isInitialFragmentSet")

        if (isInitialFragmentSet) {
            Log.d(TAG, "NAVIGATION_RESTORE: Restoring fragment state for item: $currentSelectedItemId")

            // If dynamic navigation manager is initialized, validate its state
            if (dynamicNavigationManager.isInitialized()) {
                val currentState = dynamicNavigationManager.getCurrentState()
                if (currentState != null) {
                    Log.d(TAG, "NAVIGATION_RESTORE: Dynamic navigation manager state - active: ${currentState.activeFragmentId}, visible items: ${currentState.visibleMenuItems.size}")

                    // Validate that the navigation state is consistent
                    val actualVisibleCount = (0 until binding.bottomView.menu.size()).count {
                        binding.bottomView.menu.getItem(it).isVisible
                    }
                    Log.d(TAG, "NAVIGATION_RESTORE: Actual visible menu items: $actualVisibleCount")

                    if (actualVisibleCount == 4) {
                        Log.d(TAG, "NAVIGATION_RESTORE: Navigation state is consistent, no manual restoration needed")
                        return
                    } else {
                        Log.w(TAG, "NAVIGATION_RESTORE: Navigation state inconsistent (visible: $actualVisibleCount), forcing manual restoration")

                        // Force update the navigation state to fix the inconsistency
                        Log.d(TAG, "NAVIGATION_RESTORE: Forcing navigation state update to fix menu visibility")
                        val correctedState = currentState.copy(shouldShowTransition = false)
                        // Trigger a state update to fix menu visibility
                        dynamicNavigationManager.handleUserNavigation(currentState.activeFragmentId)

                        // Re-check after forced update
                        val newVisibleCount = (0 until binding.bottomView.menu.size()).count {
                            binding.bottomView.menu.getItem(it).isVisible
                        }
                        Log.d(TAG, "NAVIGATION_RESTORE: After forced update, visible menu items: $newVisibleCount")

                        if (newVisibleCount == 4) {
                            Log.d(TAG, "NAVIGATION_RESTORE: Navigation state corrected, no manual restoration needed")
                            return
                        }
                    }
                } else {
                    Log.w(TAG, "NAVIGATION_RESTORE: Dynamic navigation manager has null state")
                }
            } else {
                Log.w(TAG, "NAVIGATION_RESTORE: Dynamic navigation manager not initialized")
            }

            // Fallback to manual restoration
            Log.d(TAG, "NAVIGATION_RESTORE: Performing manual fragment state restoration")
            binding.bottomView.selectedItemId = currentSelectedItemId
            val fragment = when (currentSelectedItemId) {
                R.id.chargeFragment -> StatsChargeFragment()
                R.id.dischargeFragment -> DischargeFragment()
                R.id.healthFragment -> HealthFragment()
                R.id.settingsFragment -> SettingsFragment()
                R.id.animationGridFragment -> AnimationGridFragment()
                else -> StatsChargeFragment()
            }
            Log.d(TAG, "NAVIGATION_RESTORE: Manual fragment state restoration to: ${fragment.javaClass.simpleName}")
            supportFragmentManager
                .beginTransaction()
                .replace(binding.navHostFragment.id, fragment)
                .commit()
        } else {
            Log.d(TAG, "NAVIGATION_RESTORE: Initial fragment not set yet, skipping restoration")
        }
    }

    override fun onResume() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: MainActivity.onResume() started at $startTime")
        Log.d(TAG, "NAVIGATION_RESTORE: onResume called - currentSelectedItemId: $currentSelectedItemId")

        super.onResume()
        Log.d(TAG, "NAVIGATION_RESTORE: onResume: Restoring fragment state and starting services")

        // Apply any necessary device-specific resume handling
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                // Handle any Xiaomi-specific issues when resuming
                Log.d(TAG, "Applying Xiaomi-specific resume handling")

                // Register the animation fix receiver
                val intentFilter = IntentFilter().apply {
                    addAction("android.intent.action.SCREEN_ON")
                    addAction("android.intent.action.USER_PRESENT")
                }
                registerReceiver(animationFixReceiver, intentFilter)

                // Re-apply layout settings for MIUI compatibility
                binding.root.requestLayout()
                binding.navHostFragment.requestLayout()
            } catch (e: Exception) {
                Log.e(TAG, "Error in Xiaomi resume handling", e)
            }
        }

        // Restore fragment state when activity resumes
        restoreFragmentState()

        // Check and show background permission dialog if needed
        checkAndShowBackgroundPermissionDialog()

        // DEPRECATED: Legacy BatteryMonitorService startup disabled
        // Using CoreBatteryStatsService for unified battery monitoring instead
        // Multiple battery services were causing resource waste and data inconsistency
        // if (!isServiceRunning(BatteryMonitorService::class.java)) {
        //     Log.d(TAG, "Starting BatteryMonitorService")
        //     val monitorIntent = Intent(this, BatteryMonitorService::class.java)
        //     try {
        //         ContextCompat.startForegroundService(this, monitorIntent)
        //         Log.d(TAG, "BatteryMonitorService started successfully")
        //     } catch (e: Exception) {
        //         Log.e(TAG, "Error starting BatteryMonitorService", e)
        //     }
        // }
        Log.d(TAG, "Using CoreBatteryStatsService for unified battery monitoring")

        // DEPRECATED: Legacy NewChargeMonitorService replaced by UnifiedBatteryNotificationService
        // Using unified notification service that consumes CoreBatteryStatsProvider
        // Log.d(TAG, "Starting NewChargeMonitorService via helper")
        // chargeMonitorServiceHelper.startService()

        // Start the unified battery notification service
        Log.d(TAG, "Starting UnifiedBatteryNotificationService via helper")
        unifiedBatteryNotificationServiceHelper.startService()

        // Defer non-critical service startup to background thread
        startNonCriticalServicesAsync()

        // Log navigation performance stats
        logNavigationPerformanceStats()

        Log.d(TAG, "STARTUP_TIMING: MainActivity.onResume() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Start non-critical services asynchronously to avoid blocking UI
     */
    private fun startNonCriticalServicesAsync() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Enhanced discharge timer service
                val currentSession = dischargeSessionRepository.currentSession.value
                if (currentSession != null && currentSession.isActive && !statsChargeRepository.statsChargeStatusFlow.first().isCharging) {
                    Log.d(TAG, "Starting EnhancedDischargeTimerService via helper")
                    enhancedDischargeTimerServiceHelper.startService()
                }

                // Charging overlay service
                withContext(Dispatchers.Main) {
                    val overlayServiceStartTime = System.currentTimeMillis()
                    if (!isServiceRunning(ChargingOverlayService::class.java)) {
                        Log.d(TAG, "Starting ChargingOverlayService")
                        val overlayIntent = Intent(this@MainActivity, ChargingOverlayService::class.java)
                        ContextCompat.startForegroundService(this@MainActivity, overlayIntent)
                    }
                    Log.d(TAG, "STARTUP_TIMING: Async overlay service check took ${System.currentTimeMillis() - overlayServiceStartTime}ms")
                }

                // Anti-thief check
                withContext(Dispatchers.Main) {
                    val antiThiefStartTime = System.currentTimeMillis()
                    if (appRepository.isAntiThiefAlertActive() && !AntiThiefUtils.isEnterPasswordActivityRunning(this@MainActivity)) {
                        Log.d(TAG, "Anti-theft alert is active, launching EnterPasswordActivity")
                        val intent = Intent(this@MainActivity, com.tqhit.battery.one.activity.password.EnterPasswordActivity::class.java)
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        startActivity(intent)
                    }
                    Log.d(TAG, "STARTUP_TIMING: Async anti-thief check took ${System.currentTimeMillis() - antiThiefStartTime}ms")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in non-critical service startup", e)
            }
        }
    }

    override fun onPause() {
        // Unregister the animation fix receiver if it was registered
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                unregisterReceiver(animationFixReceiver)
            } catch (e: Exception) {
                Log.e(TAG, "Error unregistering animation fix receiver", e)
            }
        }

        super.onPause()
    }

    /**
     * Logs navigation performance statistics for debugging.
     */
    private fun logNavigationPerformanceStats() {
        if (dynamicNavigationManager.isInitialized()) {
            Log.i(TAG, "NAVIGATION_PERFORMANCE: ${dynamicNavigationManager.getPerformanceStats()}")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy called")

        // Clean up background permission dialog
        backgroundPermissionDialog?.dismiss()
        backgroundPermissionDialog = null

        // Unregister Xiaomi-specific receiver if registered
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                unregisterReceiver(animationFixReceiver)
            } catch (e: Exception) {
                Log.d(TAG, "Animation fix receiver was not registered or already unregistered")
            }
        }

//        if (binding.bannerContainer.isEnabled)
//            applovinBannerAdManager.destroy(binding.bannerContainer)
    }

    private fun initBannerAd() {
//        if (remoteConfigHelper.getBoolean("bn_enable")) {
//            binding.bannerContainer.visibility = View.VISIBLE
//            applovinBannerAdManager.loadAd(
//                "default_bn",
//                binding.bannerContainer,
//                object : MaxAdViewAdListener {
//                    override fun onAdLoaded(p0: MaxAd) {
//                    }
//
//                    override fun onAdDisplayed(p0: MaxAd) {
//                    }
//
//                    override fun onAdHidden(p0: MaxAd) {
//                    }
//
//                    override fun onAdClicked(p0: MaxAd) {
//                    }
//
//                    override fun onAdLoadFailed(p0: String, p1: MaxError) {
//                        handler.postDelayed({ initBannerAd() }, 5000)
//                    }
//
//                    override fun onAdDisplayFailed(p0: MaxAd, p1: MaxError) {
//                    }
//
//                    override fun onAdExpanded(p0: MaxAd) {
//                    }
//
//                    override fun onAdCollapsed(p0: MaxAd) {
//                    }
//                }
//            )
//        } else {
//            binding.bannerContainer.visibility = View.GONE
//        }
    }

    /**
     * Apply specific handling for Xiaomi devices to prevent crashes
     */
    private fun applyXiaomiSpecificHandling() {
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                Log.d(TAG, "Applying Xiaomi-specific handling for MainActivity")

                // Set window flags to prevent animation conflicts
                window?.setFlags(
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )

                // Use specific drawing cache settings for better MIUI compatibility
                binding.root.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                binding.navHostFragment.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
            } catch (e: Exception) {
                Log.e(TAG, "Error applying Xiaomi handling", e)
            }
        }
    }

    /**
     * Checks if background permission dialog should be shown and displays it if needed.
     * Uses proper Android lifecycle management and rate limiting to avoid showing during inappropriate states.
     */
    private fun checkAndShowBackgroundPermissionDialog() {
        Log.d(TAG, "BackgroundPermission: Checking if background permission dialog should be shown")

        // Don't show dialog if activity is not in appropriate state
        if (isFinishing || isDestroyed) {
            Log.d(TAG, "BackgroundPermission: Activity finishing or destroyed, skipping dialog")
            return
        }

        // Don't show dialog if one is already showing
        if (backgroundPermissionDialog?.isShowing == true) {
            Log.d(TAG, "BackgroundPermission: Dialog already showing, skipping")
            return
        }

        // Check if permission dialog should be shown (includes rate limiting)
        if (BackgroundPermissionManager.shouldShowBackgroundPermissionDialog(this)) {
            Log.d(TAG, "BackgroundPermission: Showing background permission dialog")
            showBackgroundPermissionDialog()
        } else {
            // Log specific reason why dialog is not shown
            if (BackgroundPermissionManager.isIgnoringBatteryOptimizations(this)) {
                Log.d(TAG, "BackgroundPermission: Permission already granted, no dialog needed")
            } else if (BackgroundPermissionManager.isInCooldownPeriod(this)) {
                val remainingTime = BackgroundPermissionManager.getRemainingCooldownTime(this)
                val remainingMinutes = remainingTime / (60 * 1000)
                Log.d(TAG, "BackgroundPermission: Dialog in cooldown period, ${remainingMinutes} minutes remaining")
            } else {
                Log.d(TAG, "BackgroundPermission: Dialog not shown for unknown reason")
            }
        }
    }

    /**
     * Shows the background permission dialog with proper callbacks
     */
    private fun showBackgroundPermissionDialog() {
        try {
            backgroundPermissionDialog = BackgroundPermissionDialog(
                context = this,
                onPermissionGranted = {
                    Log.d(TAG, "BackgroundPermission: Permission granted via dialog")
                    // Permission granted - no additional action needed
                },
                onPermissionDenied = {
                    Log.d(TAG, "BackgroundPermission: Permission denied via dialog")
                    // Permission denied - user chose not to grant permission
                },
                onDialogClosed = {
                    Log.d(TAG, "BackgroundPermission: Dialog closed")
                    backgroundPermissionDialog = null
                }
            )

            backgroundPermissionDialog?.show()
        } catch (e: Exception) {
            Log.e(TAG, "BackgroundPermission: Error showing background permission dialog", e)
        }
    }
}
